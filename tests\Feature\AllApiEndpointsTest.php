<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use App\Models\Verification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AllApiEndpointsTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $employee;
    protected $site;
    protected $adminToken;
    protected $employeeToken;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer les données de test
        $this->admin = User::factory()->create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);
        
        $this->employee = User::factory()->create([
            'name' => 'Employee Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);
        
        $this->site = Site::factory()->create([
            'name' => 'Site Test',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
        
        // Assigner l'employé au site
        Assignment::create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);
        
        // Créer les tokens
        $this->adminToken = $this->admin->createToken('test-token')->plainTextToken;
        $this->employeeToken = $this->employee->createToken('test-token')->plainTextToken;
        
        echo "\n=== Configuration des données de test terminée ===\n";
    }

    /**
     * Test complet des endpoints d'authentification
     */
    public function test_authentication_endpoints(): void
    {
        echo "\n🔐 TEST DES ENDPOINTS D'AUTHENTIFICATION\n";
        echo "=========================================\n";
        
        // Test 1: POST /api/login - Succès
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => ['id', 'name', 'email', 'role'],
                        'token',
                        'token_type'
                    ]
                ]);
        
        $token = $response->json('data.token');
        echo "✅ POST /api/login - Login réussi\n";
        
        // Test 2: POST /api/login - Échec
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);
        
        $response->assertStatus(401)
                ->assertJson(['success' => false]);
        echo "✅ POST /api/login - Rejet des identifiants invalides\n";
        
        // Test 3: GET /api/me - Avec token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/me');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        echo "✅ GET /api/me - Profil récupéré avec token\n";
        
        // Test 4: GET /api/me - Sans token
        $response = $this->getJson('/api/me');
        $response->assertStatus(401);
        echo "✅ GET /api/me - Accès refusé sans token\n";
        
        // Test 5: POST /api/logout
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ POST /api/logout - Déconnexion réussie\n";
    }

    /**
     * Test complet des endpoints de gestion des employés
     */
    public function test_employee_management_endpoints(): void
    {
        echo "\n👥 TEST DES ENDPOINTS DE GESTION DES EMPLOYÉS\n";
        echo "==============================================\n";
        
        // Test 1: GET /api/employees - Admin
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/employees');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'email', 'role']
                    ],
                    'pagination'
                ]);
        echo "✅ GET /api/employees - Liste récupérée (admin)\n";
        
        // Test 2: GET /api/employees - Employé (refusé)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/employees');
        
        $response->assertStatus(403);
        echo "✅ GET /api/employees - Accès refusé (employé)\n";
        
        // Test 3: POST /api/employees - Création
        $newEmployee = [
            'name' => 'Nouvel Employé',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'employee'
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/employees', $newEmployee);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        
        $newEmployeeId = $response->json('data.id');
        echo "✅ POST /api/employees - Employé créé (ID: $newEmployeeId)\n";
        
        // Test 4: GET /api/employees/{id} - Consultation
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson("/api/employees/$newEmployeeId");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        echo "✅ GET /api/employees/{id} - Employé consulté\n";
        
        // Test 5: PUT /api/employees/{id} - Modification
        $updateData = [
            'name' => 'Employé Modifié',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->putJson("/api/employees/$newEmployeeId", $updateData);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['name' => 'Employé Modifié']
                ]);
        echo "✅ PUT /api/employees/{id} - Employé modifié\n";
        
        // Test 6: DELETE /api/employees/{id} - Suppression
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->deleteJson("/api/employees/$newEmployeeId");
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ DELETE /api/employees/{id} - Employé supprimé\n";
    }

    /**
     * Test complet des endpoints de pointage
     */
    public function test_pointage_endpoints(): void
    {
        echo "\n⏰ TEST DES ENDPOINTS DE POINTAGE\n";
        echo "==================================\n";
        
        // Test 1: POST /api/check-location - Dans la zone
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['is_within_range' => true]
                ]);
        echo "✅ POST /api/check-location - Position dans la zone\n";
        
        // Test 2: POST /api/check-location - Hors zone
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['is_within_range' => false]
                ]);
        echo "✅ POST /api/check-location - Position hors zone\n";
        
        // Test 3: POST /api/save-pointage - Enregistrement
        $pointageData = [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => '2024-01-15 08:00:00',
            'fin_pointage' => '2024-01-15 17:00:00',
            'debut_latitude' => 33.5731,
            'debut_longitude' => -7.5898,
            'fin_latitude' => 33.5731,
            'fin_longitude' => -7.5898
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/save-pointage', $pointageData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'user_id', 'site_id', 'debut_pointage']
                ]);
        echo "✅ POST /api/save-pointage - Pointage enregistré\n";
        
        // Test 4: GET /api/pointages - Admin
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/pointages');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'user_id', 'site_id', 'debut_pointage']
                    ],
                    'pagination'
                ]);
        echo "✅ GET /api/pointages - Liste récupérée (admin)\n";
        
        // Test 5: GET /api/pointages - Employé (refusé)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/pointages');
        
        $response->assertStatus(403);
        echo "✅ GET /api/pointages - Accès refusé (employé)\n";
    }

    /**
     * Test complet des endpoints de gestion des sites
     */
    public function test_site_management_endpoints(): void
    {
        echo "\n🏗️ TEST DES ENDPOINTS DE GESTION DES SITES\n";
        echo "===========================================\n";
        
        // Test 1: GET /api/sites - Admin
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/sites');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'latitude', 'longitude']
                    ]
                ]);
        echo "✅ GET /api/sites - Liste récupérée (admin)\n";
        
        // Test 2: POST /api/sites - Création
        $newSite = [
            'name' => 'Nouveau Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/sites', $newSite);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'name', 'latitude', 'longitude']
                ]);
        
        $newSiteId = $response->json('data.id');
        echo "✅ POST /api/sites - Site créé (ID: $newSiteId)\n";
        
        // Test 3: POST /api/assign-site - Attribution
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/assign-site', [
            'site_id' => $newSiteId,
            'user_ids' => [$this->employee->id]
        ]);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ POST /api/assign-site - Site attribué\n";
        
        // Test 4: GET /api/my-sites - Employé
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/my-sites');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'latitude', 'longitude']
                    ]
                ]);
        echo "✅ GET /api/my-sites - Sites assignés récupérés\n";
    }

    /**
     * Test complet des endpoints de vérification
     */
    public function test_verification_endpoints(): void
    {
        echo "\n📍 TEST DES ENDPOINTS DE VÉRIFICATION\n";
        echo "======================================\n";
        
        // Test 1: POST /api/request-verification - Admin
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/request-verification', [
            'user_id' => $this->employee->id,
            'message' => 'Veuillez vérifier votre position'
        ]);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ POST /api/request-verification - Demande envoyée\n";
        
        // Test 2: POST /api/verify-location - Vérification
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/verify-location', [
            'user_id' => $this->employee->id,
            'latitude' => 33.5731,
            'longitude' => -7.5898,
            'date_heure' => '2024-01-15 10:00:00'
        ]);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'user_id', 'latitude', 'longitude']
                ]);
        echo "✅ POST /api/verify-location - Vérification enregistrée\n";
        
        // Test 3: GET /api/verifications - Admin
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/verifications');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'user_id', 'latitude', 'longitude']
                    ]
                ]);
        echo "✅ GET /api/verifications - Historique récupéré\n";
    }

    /**
     * Test de validation des données
     */
    public function test_data_validation(): void
    {
        echo "\n🛡️ TEST DE VALIDATION DES DONNÉES\n";
        echo "===================================\n";
        
        // Test 1: Validation login
        $response = $this->postJson('/api/login', [
            'email' => 'invalid-email',
            'password' => '123'
        ]);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email', 'password']);
        echo "✅ Validation des données de login\n";
        
        // Test 2: Validation coordonnées GPS
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 91,  // Invalide
            'longitude' => 181 // Invalide
        ]);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude', 'longitude']);
        echo "✅ Validation des coordonnées GPS\n";
        
        // Test 3: Token invalide
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->getJson('/api/me');
        
        $response->assertStatus(401);
        echo "✅ Rejet des tokens invalides\n";
    }
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class DatabaseSetupTest extends TestCase
{
    /**
     * Test database setup and migrations
     */
    public function test_database_setup_and_migrations(): void
    {
        try {
            echo "\n=== Test de configuration de la base de données ===\n";
            
            // Test 1: Connexion de base
            echo "\n1. Test de connexion à la base de données...\n";
            $pdo = DB::connection()->getPdo();
            $this->assertNotNull($pdo, 'Database connection should not be null');
            echo "✅ Connexion réussie\n";
            
            // Test 2: Vérification de la base de données
            echo "\n2. Vérification de la base de données clockin_db...\n";
            $database = DB::select('SELECT DATABASE() as db_name')[0]->db_name;
            $this->assertEquals('clockin_db', $database, 'Should be connected to clockin_db');
            echo "✅ Connecté à clockin_db\n";
            
            // Test 3: Exécution des migrations
            echo "\n3. Exécution des migrations...\n";
            try {
                Artisan::call('migrate:fresh', ['--force' => true]);
                echo "✅ Migrations exécutées avec succès\n";
                echo Artisan::output();
            } catch (\Exception $e) {
                echo "⚠️  Erreur lors des migrations: " . $e->getMessage() . "\n";
                // Continuons quand même pour voir l'état actuel
            }
            
            // Test 4: Vérification des tables
            echo "\n4. Vérification des tables créées...\n";
            $requiredTables = [
                'users', 'sites', 'pointages', 'verifications',
                'assignments', 'logs', 'migrations', 'personal_access_tokens'
            ];
            
            $existingTables = DB::select("SHOW TABLES");
            $tableNames = array_map(function($table) {
                return array_values((array)$table)[0];
            }, $existingTables);
            
            echo "Tables trouvées: " . implode(', ', $tableNames) . "\n";
            
            foreach ($requiredTables as $table) {
                if (in_array($table, $tableNames)) {
                    echo "✅ Table '$table' existe\n";
                } else {
                    echo "❌ Table '$table' manquante\n";
                }
            }
            
            // Test 5: Exécution des seeders
            echo "\n5. Exécution des seeders...\n";
            try {
                Artisan::call('db:seed', ['--force' => true]);
                echo "✅ Seeders exécutés avec succès\n";
                echo Artisan::output();
            } catch (\Exception $e) {
                echo "⚠️  Erreur lors des seeders: " . $e->getMessage() . "\n";
            }
            
            // Test 6: Vérification des données de test
            echo "\n6. Vérification des données de test...\n";
            
            if (in_array('users', $tableNames)) {
                $userCount = DB::table('users')->count();
                echo "Nombre d'utilisateurs: $userCount\n";
                
                if ($userCount > 0) {
                    $admin = DB::table('users')->where('role', 'admin')->first();
                    if ($admin) {
                        echo "✅ Utilisateur admin trouvé: {$admin->email}\n";
                    }
                    
                    $employees = DB::table('users')->where('role', 'employee')->count();
                    echo "✅ Nombre d'employés: $employees\n";
                }
            }
            
            if (in_array('sites', $tableNames)) {
                $siteCount = DB::table('sites')->count();
                echo "Nombre de sites: $siteCount\n";
            }
            
            $this->assertTrue(true, 'Database setup test completed');
            
        } catch (\Exception $e) {
            $this->fail("Database setup failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test table structure and constraints
     */
    public function test_table_structure_and_constraints(): void
    {
        try {
            echo "\n=== Test de structure des tables ===\n";
            
            // Vérifier la structure de la table users
            echo "\n1. Structure de la table users...\n";
            $userColumns = DB::select("DESCRIBE users");
            $expectedUserColumns = ['id', 'name', 'email', 'password', 'role', 'created_at', 'updated_at'];
            
            $actualColumns = array_column($userColumns, 'Field');
            foreach ($expectedUserColumns as $column) {
                if (in_array($column, $actualColumns)) {
                    echo "✅ Colonne '$column' existe\n";
                } else {
                    echo "❌ Colonne '$column' manquante\n";
                }
            }
            
            // Vérifier les contraintes de clés étrangères
            echo "\n2. Contraintes de clés étrangères...\n";
            $constraints = DB::select("
                SELECT 
                    CONSTRAINT_NAME,
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            $expectedConstraints = [
                'pointages.user_id -> users.id',
                'pointages.site_id -> sites.id',
                'verifications.user_id -> users.id',
                'assignments.user_id -> users.id',
                'assignments.site_id -> sites.id'
            ];
            
            $actualConstraints = [];
            foreach ($constraints as $constraint) {
                $constraintStr = "{$constraint->TABLE_NAME}.{$constraint->COLUMN_NAME} -> {$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}";
                $actualConstraints[] = $constraintStr;
                echo "✅ Contrainte: $constraintStr\n";
            }
            
            $this->assertTrue(true, 'Table structure test completed');
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors du test de structure: " . $e->getMessage() . "\n";
        }
    }
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseConnectivityTest extends TestCase
{
    /**
     * Test database connection
     */
    public function test_database_connection(): void
    {
        try {
            // Test basic connection
            $pdo = DB::connection()->getPdo();
            $this->assertNotNull($pdo, 'Database connection should not be null');

            // Test if we can execute a simple query
            $result = DB::select('SELECT 1 as test');
            $this->assertEquals(1, $result[0]->test, 'Simple query should return 1');

            echo "\n✅ Database connection successful\n";

        } catch (\Exception $e) {
            $this->fail("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Test if clockin_db database exists and is accessible
     */
    public function test_clockin_database_exists(): void
    {
        try {
            // Check if we're connected to the right database
            $database = DB::select('SELECT DATABASE() as db_name')[0]->db_name;
            $this->assertEquals('clockin_db', $database, 'Should be connected to clockin_db');

            echo "\n✅ Connected to clockin_db database\n";

        } catch (\Exception $e) {
            $this->fail("Failed to verify database name: " . $e->getMessage());
        }
    }

    /**
     * Test if required tables exist
     */
    public function test_required_tables_exist(): void
    {
        $requiredTables = [
            'users', 'sites', 'pointages', 'verifications',
            'assignments', 'logs', 'migrations'
        ];

        try {
            $existingTables = DB::select("SHOW TABLES");
            $tableNames = array_map(function($table) {
                return array_values((array)$table)[0];
            }, $existingTables);

            foreach ($requiredTables as $table) {
                $this->assertContains($table, $tableNames, "Table '$table' should exist");
            }

            echo "\n✅ All required tables exist: " . implode(', ', $requiredTables) . "\n";

        } catch (\Exception $e) {
            // If tables don't exist, we'll need to run migrations
            echo "\n⚠️  Tables don't exist yet. Need to run migrations.\n";
            echo "Error: " . $e->getMessage() . "\n";

            // Try to run migrations automatically
            $this->runMigrationsIfNeeded();
        }
    }

    /**
     * Run migrations if needed
     */
    private function runMigrationsIfNeeded(): void
    {
        try {
            echo "\n🔄 Attempting to run migrations...\n";

            // Check if we can create the migrations table
            DB::statement("CREATE TABLE IF NOT EXISTS migrations (
                id int unsigned NOT NULL AUTO_INCREMENT,
                migration varchar(255) NOT NULL,
                batch int NOT NULL,
                PRIMARY KEY (id)
            )");

            echo "✅ Migrations table created/verified\n";

        } catch (\Exception $e) {
            echo "❌ Failed to create migrations table: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Test table relationships and constraints
     */
    public function test_table_relationships(): void
    {
        try {
            // Test if we can query table information
            $tables = DB::select("
                SELECT TABLE_NAME, TABLE_COMMENT
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = DATABASE()
            ");

            echo "\n📊 Database tables information:\n";
            foreach ($tables as $table) {
                echo "   - {$table->TABLE_NAME}\n";
            }

            // Test foreign key constraints
            $constraints = DB::select("
                SELECT
                    CONSTRAINT_NAME,
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");

            echo "\n🔗 Foreign key constraints:\n";
            foreach ($constraints as $constraint) {
                echo "   - {$constraint->TABLE_NAME}.{$constraint->COLUMN_NAME} -> {$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}\n";
            }

            $this->assertTrue(true, 'Table relationship test completed');

        } catch (\Exception $e) {
            echo "\n⚠️  Could not analyze table relationships: " . $e->getMessage() . "\n";
        }
    }
}

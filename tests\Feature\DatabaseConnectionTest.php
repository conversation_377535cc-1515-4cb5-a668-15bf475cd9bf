<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class DatabaseConnectionTest extends TestCase
{
    /**
     * Test de connexion à la base de données MySQL clockin_db
     */
    public function test_database_connection_to_clockin_db(): void
    {
        try {
            // Test 1: Connexion de base
            $pdo = DB::connection()->getPdo();
            $this->assertNotNull($pdo, 'La connexion à la base de données doit être établie');
            
            // Test 2: Vérifier le nom de la base de données
            $database = DB::select('SELECT DATABASE() as db_name')[0]->db_name;
            $this->assertEquals('clockin_db', $database, 'Doit être connecté à clockin_db');
            
            // Test 3: Test de requête simple
            $result = DB::select('SELECT 1 as test');
            $this->assertEquals(1, $result[0]->test, 'La requête de test doit retourner 1');
            
            echo "\n✅ Connexion à la base de données clockin_db réussie\n";
            
        } catch (\Exception $e) {
            $this->fail("Échec de la connexion à la base de données: " . $e->getMessage());
        }
    }
    
    /**
     * Test de l'existence des tables requises
     */
    public function test_required_tables_exist(): void
    {
        $requiredTables = [
            'users', 'sites', 'pointages', 'verifications',
            'assignments', 'logs', 'migrations', 'personal_access_tokens'
        ];
        
        try {
            foreach ($requiredTables as $table) {
                $exists = Schema::hasTable($table);
                $this->assertTrue($exists, "La table '$table' doit exister");
                
                if ($exists) {
                    echo "✅ Table '$table' existe\n";
                } else {
                    echo "❌ Table '$table' manquante\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors de la vérification des tables: " . $e->getMessage() . "\n";
            echo "💡 Exécutez les migrations: php artisan migrate\n";
        }
    }
    
    /**
     * Test de la structure des tables principales
     */
    public function test_table_structure(): void
    {
        try {
            // Test de la table users
            if (Schema::hasTable('users')) {
                $this->assertTrue(Schema::hasColumn('users', 'id'));
                $this->assertTrue(Schema::hasColumn('users', 'name'));
                $this->assertTrue(Schema::hasColumn('users', 'email'));
                $this->assertTrue(Schema::hasColumn('users', 'password'));
                $this->assertTrue(Schema::hasColumn('users', 'role'));
                echo "✅ Structure de la table 'users' correcte\n";
            }
            
            // Test de la table sites
            if (Schema::hasTable('sites')) {
                $this->assertTrue(Schema::hasColumn('sites', 'id'));
                $this->assertTrue(Schema::hasColumn('sites', 'name'));
                $this->assertTrue(Schema::hasColumn('sites', 'latitude'));
                $this->assertTrue(Schema::hasColumn('sites', 'longitude'));
                echo "✅ Structure de la table 'sites' correcte\n";
            }
            
            // Test de la table pointages
            if (Schema::hasTable('pointages')) {
                $this->assertTrue(Schema::hasColumn('pointages', 'id'));
                $this->assertTrue(Schema::hasColumn('pointages', 'user_id'));
                $this->assertTrue(Schema::hasColumn('pointages', 'site_id'));
                $this->assertTrue(Schema::hasColumn('pointages', 'debut_pointage'));
                $this->assertTrue(Schema::hasColumn('pointages', 'fin_pointage'));
                echo "✅ Structure de la table 'pointages' correcte\n";
            }
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors de la vérification de la structure: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test des contraintes de clés étrangères
     */
    public function test_foreign_key_constraints(): void
    {
        try {
            $constraints = DB::select("
                SELECT 
                    CONSTRAINT_NAME,
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            $this->assertNotEmpty($constraints, 'Des contraintes de clés étrangères doivent exister');
            
            echo "\n🔗 Contraintes de clés étrangères trouvées:\n";
            foreach ($constraints as $constraint) {
                echo "   - {$constraint->TABLE_NAME}.{$constraint->COLUMN_NAME} -> {$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}\n";
            }
            
            // Vérifier des contraintes spécifiques
            $expectedConstraints = [
                'pointages' => ['user_id', 'site_id'],
                'verifications' => ['user_id'],
                'assignments' => ['user_id', 'site_id']
            ];
            
            foreach ($expectedConstraints as $table => $columns) {
                foreach ($columns as $column) {
                    $found = false;
                    foreach ($constraints as $constraint) {
                        if ($constraint->TABLE_NAME === $table && $constraint->COLUMN_NAME === $column) {
                            $found = true;
                            break;
                        }
                    }
                    $this->assertTrue($found, "Contrainte manquante: {$table}.{$column}");
                }
            }
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors de la vérification des contraintes: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test des données de base (seeders)
     */
    public function test_basic_data_exists(): void
    {
        try {
            if (Schema::hasTable('users')) {
                $userCount = DB::table('users')->count();
                echo "\nNombre d'utilisateurs: $userCount\n";
                
                if ($userCount > 0) {
                    $adminCount = DB::table('users')->where('role', 'admin')->count();
                    $employeeCount = DB::table('users')->where('role', 'employee')->count();
                    
                    $this->assertGreaterThan(0, $adminCount, 'Au moins un admin doit exister');
                    echo "✅ Admins: $adminCount, Employés: $employeeCount\n";
                    
                    // Vérifier l'admin par défaut
                    $defaultAdmin = DB::table('users')
                        ->where('email', '<EMAIL>')
                        ->where('role', 'admin')
                        ->first();
                    
                    if ($defaultAdmin) {
                        echo "✅ Admin par défaut trouvé: <EMAIL>\n";
                    } else {
                        echo "⚠️  Admin par défaut non trouvé\n";
                    }
                } else {
                    echo "⚠️  Aucun utilisateur trouvé - exécutez les seeders\n";
                }
            }
            
            if (Schema::hasTable('sites')) {
                $siteCount = DB::table('sites')->count();
                echo "Nombre de sites: $siteCount\n";
                
                if ($siteCount > 0) {
                    echo "✅ Sites de test disponibles\n";
                } else {
                    echo "⚠️  Aucun site trouvé - exécutez les seeders\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors de la vérification des données: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test de performance de la base de données
     */
    public function test_database_performance(): void
    {
        try {
            $startTime = microtime(true);
            
            // Test de requête simple
            DB::select('SELECT 1');
            
            // Test de requête sur une table
            if (Schema::hasTable('users')) {
                DB::table('users')->count();
            }
            
            $endTime = microtime(true);
            $duration = ($endTime - $startTime) * 1000; // en millisecondes
            
            echo "\n⚡ Performance de la base de données: " . round($duration, 2) . "ms\n";
            
            $this->assertLessThan(1000, $duration, 'Les requêtes doivent être rapides (< 1s)');
            
            if ($duration < 100) {
                echo "✅ Performance excellente (< 100ms)\n";
            } elseif ($duration < 500) {
                echo "✅ Performance bonne (< 500ms)\n";
            } else {
                echo "⚠️  Performance acceptable mais lente\n";
            }
            
        } catch (\Exception $e) {
            echo "\n⚠️  Erreur lors du test de performance: " . $e->getMessage() . "\n";
        }
    }
}

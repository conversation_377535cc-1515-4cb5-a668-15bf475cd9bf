<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class CompleteApiTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $employee;
    protected $site;
    protected $adminToken;
    protected $employeeToken;

    protected function setUp(): void
    {
        parent::setUp();
        
        echo "\n=== Configuration des données de test ===\n";
        
        // Créer un admin
        $this->admin = User::factory()->create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);
        
        // Créer un employé
        $this->employee = User::factory()->create([
            'name' => 'Employee Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);
        
        // Créer un site
        $this->site = Site::factory()->create([
            'name' => 'Site Test',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
        
        // Assigner l'employé au site
        Assignment::create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);
        
        // Créer les tokens
        $this->adminToken = $this->admin->createToken('test-token')->plainTextToken;
        $this->employeeToken = $this->employee->createToken('test-token')->plainTextToken;
        
        echo "✅ Données de test configurées\n";
    }

    /**
     * Test complet d'authentification
     */
    public function test_authentication_flow(): void
    {
        echo "\n=== Test d'authentification ===\n";
        
        // Test 1: Login avec identifiants valides
        echo "\n1. Test de login avec identifiants valides...\n";
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => ['id', 'name', 'email', 'role'],
                        'token',
                        'token_type'
                    ]
                ]);
        
        $token = $response->json('data.token');
        $this->assertNotNull($token);
        echo "✅ Login réussi, token reçu\n";
        
        // Test 2: Récupération du profil
        echo "\n2. Test de récupération du profil...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/me');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        echo "✅ Profil récupéré avec succès\n";
        
        // Test 3: Logout
        echo "\n3. Test de logout...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ Logout réussi\n";
        
        // Test 4: Login avec identifiants invalides
        echo "\n4. Test de login avec identifiants invalides...\n";
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);
        
        $response->assertStatus(401)
                ->assertJson(['success' => false]);
        echo "✅ Rejet des identifiants invalides\n";
    }

    /**
     * Test complet de gestion des employés
     */
    public function test_employee_management(): void
    {
        echo "\n=== Test de gestion des employés ===\n";
        
        // Test 1: Liste des employés (admin)
        echo "\n1. Test de liste des employés (admin)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/employees');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'email', 'role']
                    ],
                    'pagination'
                ]);
        echo "✅ Liste des employés récupérée\n";
        
        // Test 2: Création d'un employé
        echo "\n2. Test de création d'employé...\n";
        $newEmployeeData = [
            'name' => 'Nouvel Employé',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'employee'
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/employees', $newEmployeeData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        
        $newEmployeeId = $response->json('data.id');
        echo "✅ Employé créé avec ID: $newEmployeeId\n";
        
        // Test 3: Consultation d'un employé
        echo "\n3. Test de consultation d'employé...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson("/api/employees/$newEmployeeId");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => ['id', 'name', 'email', 'role']
                ]);
        echo "✅ Employé consulté avec succès\n";
        
        // Test 4: Modification d'un employé
        echo "\n4. Test de modification d'employé...\n";
        $updateData = [
            'name' => 'Employé Modifié',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->putJson("/api/employees/$newEmployeeId", $updateData);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['name' => 'Employé Modifié']
                ]);
        echo "✅ Employé modifié avec succès\n";
        
        // Test 5: Suppression d'un employé
        echo "\n5. Test de suppression d'employé...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->deleteJson("/api/employees/$newEmployeeId");
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ Employé supprimé avec succès\n";
        
        // Test 6: Accès refusé pour employé
        echo "\n6. Test d'accès refusé pour employé...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/employees');
        
        $response->assertStatus(403);
        echo "✅ Accès correctement refusé pour employé\n";
    }

    /**
     * Test complet de pointage
     */
    public function test_pointage_functionality(): void
    {
        echo "\n=== Test de fonctionnalité de pointage ===\n";
        
        // Test 1: Vérification de localisation dans la zone
        echo "\n1. Test de vérification de localisation (dans la zone)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 33.5731,  // Même position que le site
            'longitude' => -7.5898
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['is_within_range' => true]
                ]);
        echo "✅ Localisation dans la zone confirmée\n";
        
        // Test 2: Vérification de localisation hors zone
        echo "\n2. Test de vérification de localisation (hors zone)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 34.0209,  // Position différente (Rabat)
            'longitude' => -6.8416
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => ['is_within_range' => false]
                ]);
        echo "✅ Localisation hors zone détectée\n";
        
        // Test 3: Enregistrement d'un pointage
        echo "\n3. Test d'enregistrement de pointage...\n";
        $pointageData = [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => '2024-01-15 08:00:00',
            'fin_pointage' => '2024-01-15 17:00:00',
            'debut_latitude' => 33.5731,
            'debut_longitude' => -7.5898,
            'fin_latitude' => 33.5731,
            'fin_longitude' => -7.5898
        ];
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/save-pointage', $pointageData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id', 'user_id', 'site_id', 'debut_pointage', 'fin_pointage'
                    ]
                ]);
        echo "✅ Pointage enregistré avec succès\n";
        
        // Test 4: Consultation des pointages (admin)
        echo "\n4. Test de consultation des pointages (admin)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/pointages');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'user_id', 'site_id', 'debut_pointage']
                    ],
                    'pagination'
                ]);
        echo "✅ Pointages consultés avec succès\n";
        
        // Test 5: Accès refusé pour employé
        echo "\n5. Test d'accès refusé pour employé...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/pointages');
        
        $response->assertStatus(403);
        echo "✅ Accès aux pointages correctement refusé pour employé\n";
    }

    /**
     * Test complet de gestion des sites
     */
    public function test_site_management(): void
    {
        echo "\n=== Test de gestion des sites ===\n";

        // Test 1: Liste des sites (admin)
        echo "\n1. Test de liste des sites (admin)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/sites');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'latitude', 'longitude']
                    ]
                ]);
        echo "✅ Liste des sites récupérée\n";

        // Test 2: Création d'un site
        echo "\n2. Test de création de site...\n";
        $newSiteData = [
            'name' => 'Nouveau Site Test',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/sites', $newSiteData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => ['id', 'name', 'latitude', 'longitude']
                ]);

        $newSiteId = $response->json('data.id');
        echo "✅ Site créé avec ID: $newSiteId\n";

        // Test 3: Attribution de site à des employés
        echo "\n3. Test d'attribution de site...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/assign-site', [
            'site_id' => $newSiteId,
            'user_ids' => [$this->employee->id]
        ]);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ Site attribué avec succès\n";

        // Test 4: Mes sites (employé)
        echo "\n4. Test de consultation des sites assignés (employé)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->getJson('/api/my-sites');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'name', 'latitude', 'longitude']
                    ]
                ]);
        echo "✅ Sites assignés récupérés\n";
    }

    /**
     * Test complet de vérification
     */
    public function test_verification_functionality(): void
    {
        echo "\n=== Test de fonctionnalité de vérification ===\n";

        // Test 1: Demande de vérification (admin)
        echo "\n1. Test de demande de vérification (admin)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->postJson('/api/request-verification', [
            'user_id' => $this->employee->id,
            'message' => 'Veuillez vérifier votre position'
        ]);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        echo "✅ Demande de vérification envoyée\n";

        // Test 2: Vérification de localisation
        echo "\n2. Test de vérification de localisation...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/verify-location', [
            'user_id' => $this->employee->id,
            'latitude' => 33.5731,
            'longitude' => -7.5898,
            'date_heure' => '2024-01-15 10:00:00'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id', 'user_id', 'latitude', 'longitude', 'date_heure'
                    ]
                ]);
        echo "✅ Vérification de localisation enregistrée\n";

        // Test 3: Historique des vérifications (admin)
        echo "\n3. Test d'historique des vérifications (admin)...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminToken,
        ])->getJson('/api/verifications');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'user_id', 'latitude', 'longitude', 'date_heure']
                    ]
                ]);
        echo "✅ Historique des vérifications récupéré\n";
    }

    /**
     * Test de validation des données et sécurité
     */
    public function test_data_validation_and_security(): void
    {
        echo "\n=== Test de validation et sécurité ===\n";

        // Test 1: Validation des données de login
        echo "\n1. Test de validation des données de login...\n";
        $response = $this->postJson('/api/login', [
            'email' => 'invalid-email',
            'password' => '123'  // Trop court
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email', 'password']);
        echo "✅ Validation des données de login fonctionne\n";

        // Test 2: Accès sans token
        echo "\n2. Test d'accès sans token...\n";
        $response = $this->getJson('/api/me');

        $response->assertStatus(401);
        echo "✅ Accès sans token correctement refusé\n";

        // Test 3: Token invalide
        echo "\n3. Test avec token invalide...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->getJson('/api/me');

        $response->assertStatus(401);
        echo "✅ Token invalide correctement rejeté\n";

        // Test 4: Validation des coordonnées GPS
        echo "\n4. Test de validation des coordonnées GPS...\n";
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->employeeToken,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 91,  // Invalide (> 90)
            'longitude' => 181  // Invalide (> 180)
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude', 'longitude']);
        echo "✅ Validation des coordonnées GPS fonctionne\n";
    }

    /**
     * Test d'intégrité de la base de données
     */
    public function test_database_integrity(): void
    {
        echo "\n=== Test d'intégrité de la base de données ===\n";

        // Test 1: Vérification des relations
        echo "\n1. Test des relations de base de données...\n";

        // Créer un pointage et vérifier les relations
        $pointage = Pointage::create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => '2024-01-15 08:00:00',
            'fin_pointage' => '2024-01-15 17:00:00',
            'debut_latitude' => 33.5731,
            'debut_longitude' => -7.5898,
            'fin_latitude' => 33.5731,
            'fin_longitude' => -7.5898
        ]);

        // Vérifier que les relations fonctionnent
        $this->assertNotNull($pointage->user);
        $this->assertNotNull($pointage->site);
        $this->assertEquals($this->employee->id, $pointage->user->id);
        $this->assertEquals($this->site->id, $pointage->site->id);
        echo "✅ Relations Eloquent fonctionnent correctement\n";

        // Test 2: Vérification des contraintes
        echo "\n2. Test des contraintes de base de données...\n";

        // Vérifier que l'assignment existe
        $assignment = Assignment::where('user_id', $this->employee->id)
                               ->where('site_id', $this->site->id)
                               ->first();
        $this->assertNotNull($assignment);
        echo "✅ Contraintes d'assignment respectées\n";

        // Test 3: Vérification de l'intégrité des données
        echo "\n3. Test d'intégrité des données...\n";

        $this->assertDatabaseHas('users', [
            'id' => $this->admin->id,
            'role' => 'admin'
        ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->employee->id,
            'role' => 'employee'
        ]);

        $this->assertDatabaseHas('sites', [
            'id' => $this->site->id,
            'name' => 'Site Test'
        ]);

        echo "✅ Intégrité des données vérifiée\n";
    }
}
